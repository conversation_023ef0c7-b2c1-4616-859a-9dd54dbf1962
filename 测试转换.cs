using System;
using System.Collections.Generic;
using System.Data;
using System.IO;

class Program
{
    static void Main()
    {
        // 读取ai.txt文件内容
        string aiContent = File.ReadAllText("ai.txt");
        
        // 创建字典参数
        var stringDic = new Dictionary<string, string>
        {
            ["原文"] = aiContent,
            ["转换模式"] = "转义格式"
        };
        
        var listDic = new Dictionary<string, List<string>>();
        var intDic = new Dictionary<string, int>();
        var rowDic = new Dictionary<string, Dictionary<string, string>>();
        var tableDic = new Dictionary<string, DataTable>();
        
        // 创建插件实例并运行
        var plugin = new TextConverter();
        plugin.Run(stringDic, listDic, intDic, rowDic, tableDic);
        
        // 输出转换结果
        if (stringDic.ContainsKey("转换结果"))
        {
            string result = stringDic["转换结果"];
            Console.WriteLine("转换结果：");
            Console.WriteLine(result);
            
            // 保存到转换.,txt文件
            File.WriteAllText("转换.,txt", result);
            Console.WriteLine("\n已保存到 转换.,txt 文件");
        }
        else
        {
            Console.WriteLine("转换失败");
        }
    }
}

// 将C#插件代码包装成类
public class TextConverter
{
    public void Run(Dictionary<string, string> stringDic, 
                    Dictionary<string, List<string>> listDic, 
                    Dictionary<string, int> intDic, 
                    Dictionary<string, Dictionary<string, string>> rowDic, 
                    Dictionary<string, DataTable> tableDic)
    {
        // 检查是否需要进行文本转换
        if (stringDic.ContainsKey("原文") && stringDic.ContainsKey("转换模式"))
        {
            string originalText = stringDic["原文"];
            string convertedText = ConvertTextToEscapedFormat(originalText);
            stringDic["转换结果"] = convertedText;
            return;
        }
        
        // 这里可以保留原有的其他处理逻辑...
    }
    
    /// <summary>
    /// 将文本转换为转义格式（换行符转为\n，双引号转义等）
    /// </summary>
    private string ConvertTextToEscapedFormat(string originalText)
    {
        if (string.IsNullOrEmpty(originalText))
            return string.Empty;
        
        // 转义特殊字符
        string converted = originalText
            .Replace("\\", "\\\\")  // 反斜杠转义
            .Replace("\"", "\\\"")  // 双引号转义
            .Replace("\r\n", "\\n") // Windows换行符
            .Replace("\n", "\\n")   // Unix换行符
            .Replace("\r", "\\n")   // Mac换行符
            .Replace("\t", "\\t");  // 制表符转义
        
        return converted;
    }
}
