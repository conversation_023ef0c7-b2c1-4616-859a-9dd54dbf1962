using System;
using System.Collections.Generic;
using System.Data;
using System.IO;

class Program
{
    static void Main()
    {
        // 测试数据流
        string testData = @"data: {""role"":""assistant"",""message"":""[{"",""created"":1755931080,""id"":""test"",""action"":""success"",""model"":""gpt-5""}
data: {""role"":""assistant"",""message"":""Kundendienst"",""created"":1755931080,""id"":""test"",""action"":""success"",""model"":""gpt-5""}
data: {""role"":""assistant"",""message"":""\"":["",""created"":1755931080,""id"":""test"",""action"":""success"",""model"":""gpt-5""}
data: [DONE]";

        // 创建字典参数
        var stringDic = new Dictionary<string, string>
        {
            ["html"] = testData
        };

        var listDic = new Dictionary<string, List<string>>();
        var intDic = new Dictionary<string, int>();
        var rowDic = new Dictionary<string, Dictionary<string, string>>();
        var tableDic = new Dictionary<string, DataTable>();

        // 创建插件实例并运行
        var plugin = new TestProcessor();
        plugin.Run(stringDic, listDic, intDic, rowDic, tableDic);

        // 输出结果
        if (stringDic.ContainsKey("答案"))
        {
            string result = stringDic["答案"];
            Console.WriteLine("提取结果：");
            Console.WriteLine(result);
        }
        else
        {
            Console.WriteLine("处理失败");
        }
    }
}

// 测试处理器类 - 与修复后的C#插件相同
public class TestProcessor
{
    public void Run(Dictionary<string, string> stringDic,
                    Dictionary<string, List<string>> listDic,
                    Dictionary<string, int> intDic,
                    Dictionary<string, Dictionary<string, string>> rowDic,
                    Dictionary<string, DataTable> tableDic)
    {
        // 从 stringDic["html"] 提取数据流消息，输出到 stringDic["答案"]
        if (stringDic == null) return;

        if (!stringDic.ContainsKey("html"))
        {
            stringDic["答案"] = string.Empty;
            return;
        }

        string raw = stringDic["html"] ?? string.Empty;

        // 从数据流中提取完整的消息内容
        string extractedMessage = ExtractMessageFromDataStream(raw);
        stringDic["答案"] = extractedMessage;

        if (tableDic != null)
        {
            var dt = new DataTable("答案");
            dt.Columns.Add("Key", typeof(string));
            dt.Columns.Add("Value", typeof(string));
            dt.Rows.Add("答案", extractedMessage);

            if (tableDic.ContainsKey("答案")) tableDic["答案"] = dt;
            else tableDic.Add("答案", dt);
        }
    }

    /// <summary>
    /// 从AI数据流中提取并拼接完整的消息内容
    /// </summary>
    private string ExtractMessageFromDataStream(string dataStream)
    {
        if (string.IsNullOrEmpty(dataStream))
            return string.Empty;

        System.Text.StringBuilder completeMessage = new System.Text.StringBuilder();

        // 按行分割数据流
        string[] lines = dataStream.Split(new char[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);

        foreach (string line in lines)
        {
            // 跳过空行或非data行
            if (string.IsNullOrWhiteSpace(line) || !line.StartsWith("data: "))
                continue;

            // 跳过结束标记
            if (line.Contains("[DONE]"))
                continue;

            try
            {
                // 提取JSON部分（去掉"data: "前缀）
                string jsonPart = line.Substring(6).Trim();

                // 使用正则表达式提取message字段
                string messageContent = ExtractMessageWithRegex(jsonPart);
                if (!string.IsNullOrEmpty(messageContent))
                {
                    completeMessage.Append(messageContent);
                }
            }
            catch (Exception)
            {
                // 解析失败时跳过该行
                continue;
            }
        }

        return completeMessage.ToString();
    }

    /// <summary>
    /// 使用正则表达式从JSON字符串中提取message字段的值
    /// </summary>
    private string ExtractMessageWithRegex(string jsonString)
    {
        try
        {
            // 使用正则表达式匹配 "message":"..." 模式
            System.Text.RegularExpressions.Regex regex = new System.Text.RegularExpressions.Regex(
                "\"message\"\\s*:\\s*\"([^\"]*)\""
            );

            System.Text.RegularExpressions.Match match = regex.Match(jsonString);
            if (match.Success && match.Groups.Count > 1)
            {
                string messageValue = match.Groups[1].Value;
                // 处理转义字符
                messageValue = messageValue.Replace("\\\"", "\"")
                                         .Replace("\\\\", "\\")
                                         .Replace("\\n", "\n")
                                         .Replace("\\r", "\r")
                                         .Replace("\\t", "\t");
                return messageValue;
            }
        }
        catch (Exception)
        {
            // 正则匹配失败时返回空字符串
        }

        return string.Empty;
    }
}
