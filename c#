public void Run(Dictionary<string, string> stringDic,
                Dictionary<string, List<string>> listDic,
                Dictionary<string, int> intDic,
                Dictionary<string, Dictionary<string, string>> rowDic,
                Dictionary<string, DataTable> tableDic)
{
    // 仅保留：将 stringDic["html"] 转换为转义格式，输出到 stringDic 和 tableDic
    if (stringDic == null) return;

    if (!stringDic.ContainsKey("html"))
    {
        stringDic["答案"] = string.Empty;
        return;
    }

    string raw = stringDic["html"] ?? string.Empty;

    // 如果是流式 data: ... 片段，优先提取其中的 message 内容
    string extracted = ExtractMessageFromDataStream(raw);
    string sourceText = !string.IsNullOrEmpty(extracted) ? extracted : raw;

    string convertedText = ConvertTextToEscapedFormat(sourceText);
    stringDic["答案"] = convertedText;

    if (tableDic != null)
    {
        var dt = new DataTable("答案");
        dt.Columns.Add("Key", typeof(string));
        dt.Columns.Add("Value", typeof(string));
        dt.Rows.Add("答案", convertedText);

        if (tableDic.ContainsKey("答案")) tableDic["答案"] = dt;
        else tableDic.Add("答案", dt);
    }
}

// ...removed data stream extraction - only conversion retained...
/// <summary>
/// 从可能的流式 data: {...} 片段中提取 message 字段并拼接
/// 兼容每行前缀为 "data: " 的场景，也兼容直接的 JSON 块
/// </summary>
private string ExtractMessageFromDataStream(string dataStream)
{
    if (string.IsNullOrWhiteSpace(dataStream)) return string.Empty;

    StringBuilder sb = new StringBuilder();

    // 按行处理，提取每个 JSON 片段中的 message 字符串
    string[] lines = dataStream.Split(new char[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
    var msgRegex = new Regex("\\\"message\\\"\\s*:\\s*\\\"(?<m>[^\\\"]*)\\\"", RegexOptions.Compiled);
    var jsonLineRegex = new Regex("^data:\\s*(?<json>\\{.*\\})$", RegexOptions.Compiled);

    foreach (var line in lines)
    {
        if (string.IsNullOrWhiteSpace(line)) continue;
        string l = line.Trim();
        // if line starts with data: try extract JSON
        var jm = jsonLineRegex.Match(l);
        string jsonPart = null;
        if (jm.Success) jsonPart = jm.Groups["json"].Value;
        else if (l.StartsWith("{") && l.EndsWith("}")) jsonPart = l;

        if (!string.IsNullOrEmpty(jsonPart))
        {
            var mm = msgRegex.Match(jsonPart);
            if (mm.Success) sb.Append(mm.Groups["m"].Value);
            else
            {
                // 作为回退，拼接整个 jsonPart（去掉外层引号）
                sb.Append(jsonPart);
            }
        }
        else
        {
            // line not JSON, attempt to find message=... pattern
            var simple = msgRegex.Match(l);
            if (simple.Success) sb.Append(simple.Groups["m"].Value);
        }
    }

    return sb.ToString();
}

/// <summary>
/// 将文本转换为转义格式（换行符转为\n，双引号转义等）
/// 用于将ai.txt格式的文本转换为转换.,txt格式
/// </summary>
private string ConvertTextToEscapedFormat(string originalText)
{
    if (string.IsNullOrEmpty(originalText))
        return string.Empty;

    // 转义特殊字符，按照特定顺序进行转换以避免重复转义
    string converted = originalText
        .Replace("\\", "\\\\")  // 反斜杠转义（必须首先处理）
        .Replace("\"", "\\\"")  // 双引号转义
        .Replace("\r\n", "\\n") // Windows换行符
        .Replace("\n", "\\n")   // Unix换行符
        .Replace("\r", "\\n")   // Mac换行符
        .Replace("\t", "\\t");  // 制表符转义

    return converted;
}
