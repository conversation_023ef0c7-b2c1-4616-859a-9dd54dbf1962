public void Run(Dictionary<string, string> stringDic,
                Dictionary<string, List<string>> listDic,
                Dictionary<string, int> intDic,
                Dictionary<string, Dictionary<string, string>> rowDic,
                Dictionary<string, DataTable> tableDic)
{
    // 文本转换功能：将原文转换为转义格式
    if (stringDic.ContainsKey("原文"))
    {
        string originalText = stringDic["原文"];
        string convertedText = ConvertTextToEscapedFormat(originalText);
        stringDic["转换结果"] = convertedText;
    }
}

/// <summary>
/// 将文本转换为转义格式（换行符转为\n，双引号转义等）
/// 用于将ai.txt格式的文本转换为转换.,txt格式
/// </summary>
private string ConvertTextToEscapedFormat(string originalText)
{
    if (string.IsNullOrEmpty(originalText))
        return string.Empty;

    // 转义特殊字符，按照特定顺序进行转换以避免重复转义
    string converted = originalText
        .Replace("\\", "\\\\")  // 反斜杠转义（必须首先处理）
        .Replace("\"", "\\\"")  // 双引号转义
        .Replace("\r\n", "\\n") // Windows换行符
        .Replace("\n", "\\n")   // Unix换行符
        .Replace("\r", "\\n")   // Mac换行符
        .Replace("\t", "\\t");  // 制表符转义

    return converted;
}
