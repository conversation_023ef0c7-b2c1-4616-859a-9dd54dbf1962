public void Run(Dictionary<string, string> stringDic, 
                Dictionary<string, List<string>> listDic, 
                Dictionary<string, int> intDic, 
                Dictionary<string, Dictionary<string, string>> rowDic, 
                Dictionary<string, DataTable> tableDic)
{
    // 从 stringDic["html"] 获取AI数据流
    string aiDataStream = stringDic["html"];
    
    // 解析AI数据流并提取完整消息
    string completeMessage = ParseAiDataStream(aiDataStream);

    // 清理可能的代码块包裹（``` 或 ```json）
    string cleaned = CleanCodeFences(completeMessage);

    // 验证并格式化 JSON，如果不是 JSON 则保留原文并生成可读摘要
    bool isValidJson = IsValidJson(cleaned);
    stringDic["JSON验证"] = isValidJson ? "有效" : "无效";

    if (isValidJson)
    {
        try
        {
            string pretty = PrettyPrintJsonString(cleaned);
            string summary = BuildReadableSummaryFromJsonString(cleaned);

            stringDic["答案_json"] = pretty;
            stringDic["答案"] = summary;
        }
        catch (Exception)
        {
            // 解析失败时退回原始清理字符串
            stringDic["答案"] = cleaned;
            stringDic["答案_json"] = cleaned;
        }
    }
    else
    {
        // 不是 JSON 的情况下直接返回清理后的字符串
        stringDic["答案"] = cleaned;
        stringDic["答案_json"] = cleaned;
    }
}

/// <summary>
/// 解析AI数据流，提取并拼接完整的消息内容
/// </summary>
private string ParseAiDataStream(string dataStream)
{
    StringBuilder completeMessage = new StringBuilder();
    
    // 按行分割数据流
    string[] lines = dataStream.Split(new char[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
    
        foreach (string line in lines)
    {
        // 跳过空行或非data行
        if (string.IsNullOrWhiteSpace(line) || !line.StartsWith("data: "))
            continue;
            
        try
        {
            // 提取JSON部分（去掉"data: "前缀）
                string jsonPart = line.Substring(6).Trim();

                // 有些流式返回前面是"["或"]"或"[DONE]"，跳过非对象/数组行
                if (jsonPart == "[DONE]" || jsonPart == "[]")
                    continue;

                // 尝试解析该段 JSON 并取出 message 字段（若存在）
                try
                {
                    using (var doc = JsonDocument.Parse(jsonPart))
                    {
                        if (doc.RootElement.TryGetProperty("message", out var msgEl))
                        {
                            // message 可能为字符串或对象/数组的一部分
                            if (msgEl.ValueKind == JsonValueKind.String)
                            {
                                completeMessage.Append(msgEl.GetString());
                            }
                            else
                            {
                                // 如果 message 本身是复杂类型，序列化追加
                                completeMessage.Append(msgEl.GetRawText());
                            }
                        }
                        else if (doc.RootElement.TryGetProperty("data", out var dataEl))
                        {
                            // 兼容部分流中使用 data 字段包裹 message 的情况
                            if (dataEl.ValueKind == JsonValueKind.String)
                                completeMessage.Append(dataEl.GetString());
                            else
                                completeMessage.Append(dataEl.GetRawText());
                        }
                        else
                        {
                            // 如果没有 message 字段，则把整个对象文本追加（某些实现直接给出片段）
                            completeMessage.Append(doc.RootElement.GetRawText());
                        }
                    }
                }
                catch (Exception)
                {
                    // 无法解析为 JSON 时，尝试用正则回退提取 message（保守）
                    string message = ExtractMessageFromJson(jsonPart);
                    completeMessage.Append(message);
                }
        }
        catch (Exception)
        {
            // 异常时跳过该行
            continue;
        }
    }
    
    return completeMessage.ToString();
}

/// <summary>
/// 使用正则表达式从JSON字符串中提取message字段的值
/// </summary>
private string ExtractMessageFromJson(string jsonString)
{
    // 回退方法：尽量提取 message 字符串，保留原始输入作为最后手段
    try
    {
        // 尝试用 JsonDocument 解析并读取 message
        using (var doc = JsonDocument.Parse(jsonString))
        {
            if (doc.RootElement.TryGetProperty("message", out var msgEl) && msgEl.ValueKind == JsonValueKind.String)
                return msgEl.GetString();
        }
    }
    catch (Exception)
    {
        // ignore and fallback
    }

    // 最后使用正则进行保守匹配
    try
    {
        string pattern = "\"message\"\\s*:\\s*\"([^\"]*)\"";
        Regex regex = new Regex(pattern, RegexOptions.IgnoreCase);
        Match match = regex.Match(jsonString);
        if (match.Success && match.Groups.Count > 1)
        {
            string messageValue = match.Groups[1].Value;
            messageValue = messageValue.Replace("\\\"", "\"").Replace("\\\\", "\\").Replace("\\n", "\n").Replace("\\r", "\r").Replace("\\t", "\t");
            return messageValue;
        }
    }
    catch (Exception)
    {
        // ignore
    }

    return string.Empty;
}

/// <summary>
/// 验证字符串是否为有效的JSON格式
/// </summary>
private bool IsValidJson(string jsonString)
{
    if (string.IsNullOrWhiteSpace(jsonString))
        return false;
        
    try
    {
            jsonString = jsonString.Trim();

            // 尝试直接用 JsonDocument 解析以确保严格有效
            try
            {
                using (JsonDocument.Parse(jsonString)) { }
                return true;
            }
            catch
            {
                // 解析失败则回退到简单的括号匹配判断
                if (!jsonString.StartsWith("{") && !jsonString.StartsWith("["))
                    return false;
                if (!jsonString.EndsWith("}") && !jsonString.EndsWith("]"))
                    return false;
                return IsBalancedBrackets(jsonString);
            }
    }
    catch (Exception)
    {
        return false;
    }
}

/// <summary>
/// 验证JSON字符串中的括号是否匹配
/// </summary>
private bool IsBalancedBrackets(string jsonString)
{
    try
    {
        int braceCount = 0;   // {}
        int bracketCount = 0; // []
        bool inString = false;
        bool escaped = false;
        
        foreach (char c in jsonString)
        {
            if (escaped)
            {
                escaped = false;
                continue;
            }
            
            if (c == '\\')
            {
                escaped = true;
                continue;
            }
            
            if (c == '"')
            {
                inString = !inString;
                continue;
            }
            
            if (!inString)
            {
                switch (c)
                {
                    case '{':
                        braceCount++;
                        break;
                    case '}':
                        braceCount--;
                        if (braceCount < 0) return false;
                        break;
                    case '[':
                        bracketCount++;
                        break;
                    case ']':
                        bracketCount--;
                        if (bracketCount < 0) return false;
                        break;
                }
            }
        }
        
        return braceCount == 0 && bracketCount == 0;
    }
    catch (Exception)
    {
        return false;
    }
}

/// <summary>
/// 清理三引号代码块（```）等包装
/// </summary>
private string CleanCodeFences(string s)
{
    if (string.IsNullOrWhiteSpace(s)) return s;
    string t = s.Trim();
    // 删除 ```json 或 ``` 前缀和结尾的 ```
    if (t.StartsWith("```json"))
        t = t.Substring(7).TrimStart('\r','\n');
    if (t.StartsWith("```"))
        t = t.Substring(3).TrimStart('\r','\n');
    if (t.EndsWith("```"))
        t = t.Substring(0, t.Length - 3).TrimEnd('\r','\n');
    return t;
}

/// <summary>
/// 使用简单的字符串格式化为漂亮 JSON（基于缩进，避免依赖 System.Text.Json）
/// </summary>
private string PrettyPrintJsonString(string json)
{
    if (string.IsNullOrWhiteSpace(json)) return json;
    var sb = new StringBuilder();
    bool inString = false;
    bool escaped = false;
    int indent = 0;
    for (int i = 0; i < json.Length; i++)
    {
        char c = json[i];
        if (escaped)
        {
            sb.Append(c);
            escaped = false;
            continue;
        }
        if (c == '\\') { sb.Append(c); escaped = true; continue; }
        if (c == '"') { sb.Append(c); inString = !inString; continue; }
        if (inString) { sb.Append(c); continue; }

        switch (c)
        {
            case '{':
            case '[':
                sb.Append(c);
                sb.AppendLine();
                indent++;
                sb.Append(new string(' ', indent * 2));
                break;
            case '}':
            case ']':
                sb.AppendLine();
                indent = Math.Max(0, indent - 1);
                sb.Append(new string(' ', indent * 2));
                sb.Append(c);
                break;
            case ',':
                sb.Append(c);
                sb.AppendLine();
                sb.Append(new string(' ', indent * 2));
                break;
            case ':':
                sb.Append(c);
                sb.Append(' ');
                break;
            default:
                if (!char.IsWhiteSpace(c)) sb.Append(c);
                break;
        }
    }
    return sb.ToString();
}

/// <summary>
/// 基于字符串的简单 JSON 摘要生成。
/// 假设顶层是对象，值为数组，数组项是对象并包含 text/url 等字段。
/// 使用正则提取各部分以避免引用额外程序集。
/// </summary>
private string BuildReadableSummaryFromJsonString(string json)
{
    var sb = new StringBuilder();
    if (string.IsNullOrWhiteSpace(json)) return string.Empty;

    // 寻找顶层键： "Key": [ ... ]
    var objPattern = new Regex("\"(?<key>[^\"]+)\"\s*:\s*\\[", RegexOptions.Compiled);
    var matches = objPattern.Matches(json);
    int lastIndex = 0;
    foreach (Match m in matches)
    {
        string key = m.Groups["key"].Value;
        sb.AppendLine(key);

        // 找到该数组的起始位置
        int start = m.Index + m.Length - 1; // 指向 [
        int end = FindMatchingBracket(json, start);
        if (end <= start) continue;
        string arrayContent = json.Substring(start + 1, end - start - 1);

        // 提取数组内的对象项
        var itemPattern = new Regex("\{(?<obj>[^\}]*)\}", RegexOptions.Compiled);
        var itemMatches = itemPattern.Matches(arrayContent);
        foreach (Match im in itemMatches)
        {
            string objText = im.Groups["obj"].Value;
            // 尝试提取 text 和 url
            string text = ExtractJsonStringValue(objText, "text");
            string url = ExtractJsonStringValue(objText, "url");
            if (!string.IsNullOrEmpty(text))
            {
                sb.Append("  - ");
                sb.Append(text);
                if (!string.IsNullOrEmpty(url)) sb.Append(" — ").Append(url);
                sb.AppendLine();
            }
            else
            {
                sb.AppendLine("  - {" + objText.Trim() + "}");
            }
        }

        lastIndex = end;
    }

    // 如果没有匹配到结构，返回原始 JSON 的前几百字符作为回退
    string result = sb.ToString().Trim();
    if (string.IsNullOrEmpty(result))
    {
        return json.Length > 1000 ? json.Substring(0, 1000) + "..." : json;
    }
    return result;
}

// 帮助函数：从对象片段中提取字段值（假设简单双引号包裹）
private string ExtractJsonStringValue(string objContent, string fieldName)
{
    try
    {
        var p = new Regex("\"" + Regex.Escape(fieldName) + "\"\\s*:\\s*\"(?<val>[^\"]*)\"", RegexOptions.IgnoreCase);
        var m = p.Match(objContent);
        if (m.Success) return m.Groups["val"].Value;
    }
    catch { }
    return null;
}

// 找到匹配的 ] 或 } 的位置
private int FindMatchingBracket(string s, int openPos)
{
    if (openPos < 0 || openPos >= s.Length) return -1;
    char open = s[openPos];
    char close = open == '[' ? ']' : open == '{' ? '}' : '\0';
    if (close == '\0') return -1;

    int depth = 0;
    bool inString = false;
    bool escaped = false;
    for (int i = openPos; i < s.Length; i++)
    {
        char c = s[i];
        if (escaped) { escaped = false; continue; }
        if (c == '\\') { escaped = true; continue; }
        if (c == '"') { inString = !inString; continue; }
        if (inString) continue;
        if (c == open) depth++;
        else if (c == close)
        {
            depth--;
            if (depth == 0) return i;
        }
    }
    return -1;