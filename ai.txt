## 任务描述
将新的底部导航数据智能替换到现有JSON结构中，自动分配数据并生成德语标题。

## 输入格式
- **原始JSON**: `[{"Help":[{"text":"Menu item 1","url":"#","_id":"63aba03"},{"text":"Menu item 2","url":"#","_id":"62f5747"},{"text":"Menu item 3","url":"#","_id":"1ddd383"}],"Useful Links":[{"text":"Menu item 1","url":"#","_id":"80ec45f"},{"text":"Menu item 2","url":"#","_id":"6828720"},{"text":"Menu item 3","url":"#","_id":"b04ef6b"},{"text":"Menu item 3","url":"#","_id":"ae5149c"},{"text":"Menu item 3","url":"#","_id":"aeb05c8"},{"text":"Menu item 3","url":"#","_id":"448a1e9"},{"text":"Menu item 3","url":"#","_id":"0e8f25a"}]}]` - 包含现有分组结构的JSON数据
- **新数据数组**: `[{"text":"ÜBER UNS","url":"/uber-uns"},{"text":"Kontakt","url":"/kontakt"},{"text":"Datenschutzerklärung","url":"/datenschutzerklarung"},{"text":"Rückgabebedingungen","url":"/ruckgabebedingungen"},{"text":"Geschaftsbedingungen","url":"/geschaftsbedingungen"},{"text":"Versandinformationen","url":"/versandinformationen"},{"text":"Uber Cookies","url":"/uber-cookies"},{"text":"AGB","url":"/agb"},{"text":"Zahlungsmoglichkeiten","url":"/zahlungsmoglichkeiten"},{"text":"SiteMap","url":"/sitemap.xml"}]` - 待替换的底部信息数组（每项含text、url字段）

## 核心处理规则

### 数据分配策略
1. 分析原JSON中的分组数量
2. 将新数据平均分配到各分组（总数÷分组数）
3. 余数数据依次分配给前几个分组

### 德语标题生成规则
根据该组数据的内容特征，从以下类别选择合适的德语标题：

### 数据结构要求
- 保持原JSON的整体结构不变
- 每条记录必须包含：`text`、`url`、`_id`
- `_id`使用7-8位随机字符串（字母+数字组合）

## 输出规范
**重要**: 仅输出替换后的完整JSON数据，不包含任何解释说明或格式标记。

## 处理示例
```

标题: 基于内容语义生成对应德语标题
输出: 纯JSON格式数据
```

按上述规则处理并直接输出最终JSON结果。